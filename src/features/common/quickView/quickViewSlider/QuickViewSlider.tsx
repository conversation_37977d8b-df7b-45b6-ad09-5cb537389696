import { <PERSON><PERSON>, <PERSON> } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, EffectFade } from "swiper/modules";
import Link from "next/link";
import Image from "next/image";
import styles from "./QuickViewSlider.module.scss";
import { QuickviewSlider } from "@interfaces/common.inteface";
import getConfig from "next/config";
import { useTranslation } from "next-i18next";
import ImageIcon from "@mui/icons-material/Image";
import { PAGEURLS } from "@constants/common";
import useAppRouter from "@features/common/router.context";



// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method QuickViewSlider
 * @description Render slider component of the quick view
 * @returns {JSX.Element}
 */
const QuickViewSlider = ({
  brandSlug,
  brandLabel,
  denominationRange,
  imageGallery,
  brandImageData,
}: QuickviewSlider): JSX.Element => {
  // #. Get translations
  const { t } = useTranslation("common");

  // #. Get state of router context
  const {
    state: { regionUrl },
  } = useAppRouter();

  return (
    <div
      className={styles["quick-view-slider"]}
      data-testid="quickViewSliderWrap"
    >
      <div className={styles["quick-view-slider-infobar"]}>
        {brandLabel ? (
          <Chip
            label={brandLabel}
            className={styles["online-friendly"]}
            data-testid="quickViewSliderLabel"
          />
        ) : (
          <div>&nbsp;</div>
        )}
        <span
          className={styles["amount-info"]}
          data-testid="quickViewSliderAmount"
        >
          {denominationRange?.indexOf("-") !== -1
            ? `${t("minMax")} ${denominationRange}`
            : denominationRange}
        </span>
      </div>
      <div className={styles["quick-view-slider-swiper"]}>
        {!!imageGallery?.edges?.length ? (
          <Swiper
            // install Swiper modules
            modules={[Autoplay, Pagination, EffectFade]}
            dir="right"
            slidesPerView={1}
            spaceBetween={10}
            pagination={{ clickable: true, type: "bullets" }}
            autoplay={{
              delay: 2000,
              disableOnInteraction: false,
            }}
            className={`swiper-quick-view ${styles["swiper-quick-view"]}`}
            effect="fade"
          >
            {imageGallery.edges.map(({ node: { image, caption } }, index) => (
              <SwiperSlide key={index}>
                {!!image ? (
                  <Link legacyBehavior href={`${regionUrl}${PAGEURLS.BRAND}/${brandSlug}`}>
                    <a className="rounded" data-testid="quickViewSliderLink">
                      <Image
                        blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                        placeholder="blur"
                        src={image}
                        width={450}
                        height={287}
                        alt={caption}
                        layout={"fixed"}
                        className="rounded"
                        data-testid="quickViewSliderImage"
                        unoptimized={true}
                      />
                    </a>
                  </Link>
                ) : (
                  ""
                )}
              </SwiperSlide>
            ))}
          </Swiper>
        ) : brandImageData ? (
          <Link legacyBehavior href={`${regionUrl}${PAGEURLS.BRAND}/${brandSlug}`}>
            <a>
              <Image
                blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                placeholder="blur"
                src={brandImageData}
                width={450}
                height={287}
                layout={"fixed"}
                className="rounded"
                unoptimized={true}
                data-testid="quickViewSingleImage"
                alt=""
              />
            </a>
          </Link>
        ) : (
          <Avatar variant="rounded" data-testid="quickViewSingleAvatar">
            <ImageIcon />
          </Avatar>
        )}
      </div>
    </div>
  );
};

export default QuickViewSlider;
