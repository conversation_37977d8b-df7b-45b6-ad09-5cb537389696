import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { useTranslation } from "next-i18next";
import { Navigation } from "swiper/modules";
import type { Swiper as SwiperType } from 'swiper';
import { SliderInterface } from "@interfaces/common.inteface";
import CategoryCardItem from "./categoryCardItem/CategoryCardItem";
import styles from "./CardsSlider.module.scss";
import getDirection from "@utils/getDirection";
import { PAGEURLS, SLIDER_TYPE } from "@constants/common";
import GiftCard from "@features/common/giftCard/GiftCard";
import useAppRouter from "@features/common/router.context";
import RedemtionTag from "@features/common/redemptionTag/RedemptionTag";
import CardGrid from "../cardGrid/CardGrid";
import { BRAND_FILTER_TYPE } from "@constants/allBrandsConfig";
import Link from "next/link";
import getConfig from "next/config";
import Image from "next/image";



interface CardSlider {
  cardsList: SliderInterface;
  clientRenderedData?: SliderInterface | any;
  loading?: boolean;
  slidesPerView?: number;
  spaceBetween?: number;
  mdSlidesPerView?: number;
  mdSpaceBetween?: number;
  lgSlidesPerView?: number;
  lgSpaceBetween?: number;
  smSlidesPerView?: number;
  smSpaceBetween?: number;
  index?: number;
}
/**
 * @method CardsSlider
 * @description Swiper slide component
 * @param CardSlider : Swiper slide params
 * @returns {JSX.Element}
 */

const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const CardsSlider = ({
  cardsList,
  clientRenderedData,
  loading,
  slidesPerView = 3,
  spaceBetween = 20,
  mdSlidesPerView = 4,
  mdSpaceBetween = 25,
  lgSlidesPerView = 4,
  lgSpaceBetween = 5,
  smSlidesPerView = 2.3,
  smSpaceBetween = 10,
  index,
}: CardSlider): JSX.Element => {
  // translations
  const { t } = useTranslation("common");
  const [nextDisabled, setNextDisabled] = useState<string>("");
  const [prevDisabled, setPrevDisabled] = useState<string>("disabled");
  const [rightShadowVisible, setRightShadowVisible] = useState<boolean>(false);
  const [leftShadowVisible, setLeftShadowVisible] = useState<boolean>(false);

  // #. Get the router context
  const {
    router,
    state: { locale },
  } = useAppRouter();

  // setup for current direction
  const direction = getDirection(locale);
  // cardlist data

  const ctaText = locale === "ar" ? cardsList?.ctaTextAr : cardsList?.ctaText;
  const heading = cardsList?.heading;
  const sliderType = cardsList?.sliderType;
  const sliderBrand = cardsList?.sliderBrand;
  const sliderCategories = cardsList?.sliderCategories;
  const seoName = cardsList?.category?.seoName;
  const isScrollable = cardsList?.isScrollable;
  const slug = cardsList?.slug;
  const enableCta = cardsList?.enableCta;

  // #.Function to find redemptionBadges on CSR based on heading
  const getRedemptionBadgeFromClient = () => {
    const foundItem = clientRenderedData?.find(
      (item: any) => item.heading === heading
    );
    return foundItem?.redemptionBadges;
  };

  const redemptionBadges = getRedemptionBadgeFromClient();

  const navIconFw = `${imageBaseUrl}/icons/arrow-circle-fw.svg`;
  const navIconBack = `${imageBaseUrl}/icons/arrow-circle-back.svg`;

  /**
   * Removing 'disabled' class from prev nav when transition slide to next
   */
  const slideNextTransitionStart = () => {
    setPrevDisabled("");
  };

  /**
   * Removing 'disabled' class from next nav when transition slide to prev
   */
  const slidePrevTransitionStart = () => {
    setNextDisabled("");
  };

  /**
   * Adding 'disabled' class on slide reach beginning
   */
  const reachBeginning = () => {
    setPrevDisabled("disabled");
    setLeftShadowVisible(false);
    //setRightShadowVisible(true);
  };

  /**
   * Adding 'disabled' class on slide reach End
   */
  const reachEnd = () => {
    setNextDisabled("disabled");
    setRightShadowVisible(false);
    //setLeftShadowVisible(true);
  };

  /**
   * Swiper navigation HTML div Element reference
   */
  const prevRef = useRef<HTMLDivElement>(null);
  const nextRef = useRef<HTMLDivElement>(null);

  // autoplay configuration
  const autoplayConfig =
    sliderType === SLIDER_TYPE.CATEGORY
      ? { disableOnInteraction: false }
      : false;

  const pageRedirection = () => {
    router.push(`/${BRAND_FILTER_TYPE.CATEGORY}/${slug}${PAGEURLS.ALL_BRANDS}`);
  };

  return (
    <>
      <style jsx>{`
            


            .category-slider :global(.swiper-button-next),
            .category-slider :global(.swiper-button-prev) {
              width: 50px;
              height: 50px;
              background-color: #000;
              border-radius: 50%;
            }

            .category-slider :global(.swiper-container) {
              height: 100%;
              overflow: visible;
              position: unset;
            }

             
          `}</style>
      <div
        className={`${
          sliderType === SLIDER_TYPE.CATEGORY
            ? styles["catergory-wrapper"]
            : ""
        }`}
      >
        <div
          className={`cards-slider ${styles["cards-slider"]} ${
            sliderType === SLIDER_TYPE.CATEGORY ? "category-slider" : ""
          }`}
          id="e2eTestingCardSliderWrapper"
          data-testid="cardSliderWrapper"
        >
          <div className={`container ${styles["cards-slider__container"]}`}>
            <div className={styles["cards-slider__header-container"]}>
              <div className={styles["cards-slider__header"]}>
                {index === 0 ? (
                  <h1
                    className={styles["right-border"]}
                    data-testid="cardSliderHeading"
                  >
                    {heading}
                  </h1>
                ) : (
                  <h2
                    className={styles["right-border"]}
                    data-testid="cardSliderHeading"
                  >
                    {heading}
                  </h2>
                )}
                <div className={styles["cards-slider__header-capsule"]}>
                  {redemptionBadges?.map(
                    (badge: { type: string; label: string }) => (
                      <RedemtionTag
                        type={badge?.type}
                        label={badge?.label}
                        loading={loading}
                        key={badge.type}
                      />
                    )
                  )}
                </div>
              </div>
              {isScrollable && (
                <div
                  className={`${styles["swiper-nav-wrapper"]} ${
                    styles[
                      sliderType === SLIDER_TYPE.BRAND ? "gift" : "category"
                    ]
                  }`}
                >
                  <div
                    ref={prevRef}
                    className={`${styles["prev"]} prev-nav ${styles[prevDisabled]}`}
                    data-testid="prevNav"
                  >
                    <Image src={navIconBack} height={32} width={32} alt="image"/>
                  </div>
                  <div
                    ref={nextRef}
                    className={`${styles["next"]} next-nav ${styles[nextDisabled]}`}
                    data-testid="nextNav"
                  >
                    <Image src={navIconFw} height={32} width={32} alt="image" />
                  </div>
                </div>
              )}
            </div>
            {(sliderBrand || sliderCategories) && (
              <div
                className={`${styles["cards-slider__swiper"]} home-swiper`}
                data-testid="swiperSliderWrapper"
              >
                {!isScrollable ? (
                  <CardGrid
                    data={sliderBrand}
                    clientRenderedData={clientRenderedData}
                    loading={loading}
                    seoName={seoName}
                    pageRedirection={pageRedirection}
                    slug={slug}
                    ctaText={ctaText}
                    enableCta={enableCta}
                  />
                ) : (
                  <Swiper
                    // install Swiper modules
                    modules={[Navigation]}
                    key={locale}
                    dir={direction}
                    speed={1000}
                    slidesPerView={
                      sliderType === SLIDER_TYPE.BRAND ? slidesPerView : 5
                    }
                    spaceBetween={spaceBetween}
                    slidesPerGroup={Math.round(slidesPerView - 1)}
                    navigation={{
                      prevEl: prevRef.current ? prevRef.current : undefined,
                      nextEl: nextRef.current ? nextRef.current : undefined,
                    }}
                    onInit={(swiper: any) => {
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      // @ts-ignore
                      // eslint-disable-next-line no-param-reassign
                      swiper.params.navigation.prevEl = prevRef.current;
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      // @ts-ignore
                      // eslint-disable-next-line no-param-reassign
                      swiper.params.navigation.nextEl = nextRef.current;
                      swiper.navigation.update();
                    }}
                    breakpoints={{
                      768: {
                        slidesPerView:
                          sliderType === SLIDER_TYPE.BRAND
                            ? smSlidesPerView
                            : 5,
                        spaceBetween: smSpaceBetween,
                        slidesPerGroup: Math.round(smSlidesPerView - 1),
                      },
                      1065: {
                        slidesPerView:
                          sliderType === SLIDER_TYPE.BRAND
                            ? mdSlidesPerView
                            : 6,
                        spaceBetween: mdSpaceBetween,
                        slidesPerGroup: Math.round(mdSlidesPerView - 1),
                      },
                      1321: {
                        slidesPerView:
                          sliderType === SLIDER_TYPE.BRAND
                            ? lgSlidesPerView
                            : 7,
                        spaceBetween: lgSpaceBetween,
                        slidesPerGroup: Math.round(lgSlidesPerView - 1),
                      },
                    }}
                    className={`rounded ${
                      styles["cards-slider__swiper-container"]
                    } ${
                      sliderType === SLIDER_TYPE.BRAND
                        ? styles["gift-swiper-container"]
                        : styles["category-swiper-container"]
                    }`}
                    onSlideChange={(swiper) => {
                      // category slider stop autoslide when its reach active index 15

                      if (
                        swiper?.activeIndex >= 15 &&
                        sliderType === SLIDER_TYPE.CATEGORY
                      ) {
                        swiper.autoplay.stop();
                      } else {
                        swiper.autoplay.start();
                      }

                      if (swiper.activeIndex) {
                        setLeftShadowVisible(true);
                        //setRightShadowVisible(true);
                      }
                    }}
                    onSlideNextTransitionStart={slideNextTransitionStart}
                    onSlidePrevTransitionStart={slidePrevTransitionStart}
                    onReachBeginning={reachBeginning}
                    onReachEnd={reachEnd}
                    autoplay={
                      sliderType === SLIDER_TYPE.CATEGORY
                        ? false
                        : autoplayConfig
                    }
                  >
                    {sliderType === SLIDER_TYPE.BRAND &&
                      sliderBrand?.edges.map((props, index: number) => (
                        <SwiperSlide
                          key={index}
                          data-testid="cardSliderBrandSlide"
                        >
                          <GiftCard
                            data={props?.node?.brandData?.brand}
                            clientRenderedData={clientRenderedData}
                            loading={loading}
                            seoName={seoName}
                          />
                        </SwiperSlide>
                      ))}
                    {sliderType === SLIDER_TYPE.CATEGORY &&
                      sliderCategories?.edges.map((props, index: number) => (
                        <SwiperSlide
                          key={index}
                          data-testid="cardSliderCategorySlide"
                        >
                          <CategoryCardItem {...props} />
                        </SwiperSlide>
                      ))}

                    {rightShadowVisible && (
                      <>
                        <div
                          className={`${styles["slider-right-shadow-after"]} ${
                            styles[
                              "shadow-right-after-" +
                                (sliderType === SLIDER_TYPE.CATEGORY
                                  ? "category"
                                  : "gift")
                            ]
                          }`}
                        ></div>
                        <div
                          className={`${styles["slider-right-shadow-before"]} ${
                            styles[
                              "shadow-right-before-" +
                                (sliderType === SLIDER_TYPE.CATEGORY
                                  ? "category"
                                  : "gift")
                            ]
                          }`}
                        ></div>
                      </>
                    )}
                    {enableCta && (
                      <Link legacyBehavior
                        href={`/${BRAND_FILTER_TYPE.CATEGORY}/${slug}${PAGEURLS.ALL_BRANDS}`}
                      >
                        <a className={styles["see-all-href"]}>
                          {ctaText}
                          <span
                            className={`${styles["icon-wrapper"]} button-icon-wrapper`}
                          >
                            <i className={`icon-arrow-forward`}></i>
                          </span>
                        </a>
                      </Link>
                    )}
                  </Swiper>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default CardsSlider;