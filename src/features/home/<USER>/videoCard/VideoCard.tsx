import React, { useState } from "react";
import { Dialog, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/system";
import styles from "./VideoCard.module.scss";
import getConfig from "next/config";

/**
 * @method Testimonial
 * @description Testimonial wrapper element component
 * @param data : Testimonials data
 * @returns {JSX.Element}
 */

const VideoCard = ({ values }: any): JSX.Element => {
  // taking public image config url
  const {
    publicRuntimeConfig: { imageBaseUrl },
  } = getConfig();

  const youtubeLogo = `${imageBaseUrl}/icons/youtube.svg`;
  const videoUrl = values?.node?.link;
  const matchResult = videoUrl?.match(/[\w\-]{11,}/);
  const ytImage = matchResult ? matchResult[0] : null;

  // material ui theme
  const theme = useTheme();

  // material UI mediaQuery
  const fullScreen = useMediaQuery(theme.breakpoints.down("xs"));

  // youtube popup
  const [youtubeVideo, setYoutubeVideo] = useState(false);

  /**
   * youtube dropdown event
   * @param event : mouse event
   */
  const youtubeVideoOpen = () => {
    setYoutubeVideo(true);
  };

  /**
   * youtube close
   */
  const youtubeVideoClose = () => {
    setYoutubeVideo(false);
  };

  return (
    <>
      <li
        className={`testimonial ${styles["testimonial"]}`}
        id="e2eTestingTestimonialWrapper"
        data-testid="testimonials"
      >
        <div className={`container ${styles["testimonial__container"]}`}>
          {!!videoUrl && (
            <div
              className={`${styles["testimonial__video"]}`}
              onClick={youtubeVideoOpen}
            >
              <a
                className={`${styles["testimonial__popup-event-handler"]}`}
                onClick={youtubeVideoOpen}
                id="e2eTestingTestimonialYTPlayerOpen"
                data-testid="ytLink"
              >
                <img
                  src={youtubeLogo}
                  alt="icon"
                  className={styles["play-icon"]}
                />
                {(ytImage && ytImage.length) == 11 && (
                  <img
                    src={`//img.youtube.com/vi/${ytImage}/0.jpg`}
                    alt=""
                    id="e2eTestingTestimonialYTPlayerImage"
                    data-testid="ytImage"
                  />
                )}
              </a>
            </div>
          )}
        </div>
      </li>

      <Dialog
        fullScreen={fullScreen}
        open={youtubeVideo}
        onClose={youtubeVideoClose}
        className="rounded-22"
        aria-labelledby="download"
        data-testid="ytPopup"
      >
        <a
          className={`icon-close ${styles["icon-close"]}`}
          onClick={youtubeVideoClose}
          id="e2eTestingTestimonialYTPlayerClosee"
          data-testid="ytPopupClose"
        ></a>
        <iframe
          width="600"
          height="480"
          allowFullScreen
          className={`${styles["youtube-iframe"]}`}
          src={videoUrl}
          id="e2eTestingTestimonialYTPlayerIframe"
          data-testid="ytPopupIframe"
        ></iframe>
      </Dialog>
    </>
  );
};

export default VideoCard;
