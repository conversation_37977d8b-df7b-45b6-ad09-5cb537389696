import React, { useState } from "react";
import PlayCircleFilledIcon from "@mui/icons-material/PlayCircleFilled";
import { Dialog, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/system";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import { TestimonialReviewNode } from "@interfaces/common.inteface";
import TestimonialSlider from "./testimonialSlider/TestimonialSlider";
import styles from "./Testimonial.module.scss";
import getDirection from "@utils/getDirection";
import { TestimonialInterface } from "@features/home/<USER>/testimonial.interface";
import { useQuery } from "@apollo/client";
import { TESTIMONIAL_QUERY } from "./testimonial.query";
import TestimonialSkeleton from "../contentLoader/testimonialSkeleton/TestimonialSkeleton";
import useAppRouter from "@features/common/router.context";



/**
 * @method Testimonial
 * @description Testimonial wrapper element component
 * @param data : Testimonials data
 * @returns {JSX.Element}
 */

const Testimonial = (): JSX.Element => {
  // getting locale
  // use router
  const {
    state: { region, locale },
    router,
  } = useAppRouter();
  const defaultRegion = region.toUpperCase();

  // getting query data
  const { loading, error, data } = useQuery<TestimonialInterface>(
    TESTIMONIAL_QUERY,
    {
      variables: {
        country_Code: defaultRegion,
      },
      context: {
        clientName: "webstore-with-cdn",
      },
      skip: !Boolean(defaultRegion),
    }
  );

  const videoUrl =
    data?.testimonials?.edges[0]?.node?.testimonialVideo?.edges[0]?.node?.link;

  const ytImage = videoUrl?.match(/[\w\-]{11,}/)![0];

  const title = data?.testimonials?.edges[0]?.node.heading;

  const testimonials: TestimonialReviewNode[] | undefined =
    data?.testimonials?.edges[0]?.node?.testimonialReview?.edges;

  // setup for current direction
  const direction = getDirection(locale);

  // material ui theme
  const theme = useTheme();

  // material UI mediaQuery
  const fullScreen = useMediaQuery(theme.breakpoints.down("xs"));

  // youtube popup
  const [youtubeVideo, setYoutubeVideo] = useState(false);

  /**
   * youtube dropdown event
   * @param event : mouse event
   */
  const youtubeVideoOpen = () => {
    setYoutubeVideo(true);
  };

  /**
   * youtube close
   */
  const youtubeVideoClose = () => {
    setYoutubeVideo(false);
  };

  return (
    <>
      {loading && <TestimonialSkeleton />}
      {data && (
        <>
          {(!!testimonials?.length || !!videoUrl) && (
            <div
              className={`testimonial ${styles["testimonial"]}`}
              id="e2eTestingTestimonialWrapper"
              data-testid="testimonials"
            >
              <div className={`container ${styles["testimonial__container"]}`}>
                {!!testimonials?.length && (
                  <div className={`${styles["testimonial__content"]}`}>
                    <div className={styles["testimonial__content__top"]}>
                      <i className={`icon-quotes ${styles["quote-icon"]}`}></i>
                      <h2 data-testid="title">{title}</h2>
                    </div>
                    <div className={`${styles["testimonial__box"]}`}>
                      <Swiper
                        className={`${styles["testimonial__slider"]}`}
                        // install Swiper modules
                        modules={[Autoplay]}
                        dir={direction}
                        autoplay={{
                          delay: 2000,
                          disableOnInteraction: false,
                          pauseOnMouseEnter: true,
                        }}
                        spaceBetween={200}
                      >
                        {testimonials?.map(
                          (
                            testimonial: TestimonialReviewNode,
                            index: number
                          ) => (
                            <SwiperSlide
                              key={index}
                              data-testid="testimonialsSlide"
                            >
                              <TestimonialSlider {...testimonial} />
                            </SwiperSlide>
                          )
                        )}
                      </Swiper>
                    </div>
                  </div>
                )}
                {!!videoUrl && (
                  <div
                    className={`${styles["testimonial__video"]}`}
                    onClick={youtubeVideoOpen}
                  >
                    <a
                      className={`${styles["testimonial__popup-event-handler"]}`}
                      onClick={youtubeVideoOpen}
                      id="e2eTestingTestimonialYTPlayerOpen"
                      data-testid="ytLink"
                    >
                      <PlayCircleFilledIcon
                        className={`${styles["play-icon"]}`}
                      />
                      {(ytImage && ytImage.length) == 11 && (
                        <img
                          src={`//img.youtube.com/vi/${ytImage}/0.jpg`}
                          alt=""
                          id="e2eTestingTestimonialYTPlayerImage"
                          data-testid="ytImage"
                        />
                      )}
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}
          <Dialog
            fullScreen={fullScreen}
            open={youtubeVideo}
            onClose={youtubeVideoClose}
            className="rounded-22"
            aria-labelledby="download"
            data-testid="ytPopup"
          >
            <a
              className={`icon-close ${styles["icon-close"]}`}
              onClick={youtubeVideoClose}
              id="e2eTestingTestimonialYTPlayerClosee"
              data-testid="ytPopupClose"
            ></a>
            <iframe
              width="600"
              height="480"
              allowFullScreen
              className={`${styles["youtube-iframe"]}`}
              src={videoUrl}
              id="e2eTestingTestimonialYTPlayerIframe"
              data-testid="ytPopupIframe"
            ></iframe>
          </Dialog>
        </>
      )}
    </>
  );
};

export default Testimonial;
