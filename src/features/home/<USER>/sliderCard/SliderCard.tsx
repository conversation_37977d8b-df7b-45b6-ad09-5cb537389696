import { useTranslation } from "next-i18next";
import getConfig from "next/config";
import React, { useEffect, useRef, useState } from "react";
import styles from "./SliderCard.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import type { Swiper as SwiperType } from 'swiper';
import SliderItem from "./SliderItem";
import getDirection from "@utils/getDirection";
import useAppRouter from "@features/common/router.context";
import ReviewSliderSkelton from "@features/home/<USER>/reviewSliderSkelton/ReviewSliderSkelton";
import Image from "next/image";



const SliderCard = ({ testimonialReview, loading }: any): JSX.Element => {
  const [orderedList, setOrderedList] = useState<any[]>([]);

  // taking public image config url
  const {
    publicRuntimeConfig: { imageBaseUrl },
  } = getConfig();

  // #. Get the router context
  const {
    router,
    state: { locale },
  } = useAppRouter();

  // #. Get translations
  const { t } = useTranslation("common");

  // Images
  const backgroundImage = `${imageBaseUrl}/images/reviews-background.png`;
  const navIcon = `${imageBaseUrl}/icons/arrow-circle-fw.svg`;

  // setup for current direction
  const direction = getDirection(locale);

  useEffect(() => {
    if (testimonialReview?.edges?.length) {
      // Sort API response data according to orderNumber
      const sortedList = [...testimonialReview.edges].sort(
        (a: any, b: any) => (a?.node?.orderNumber || 0) - (b?.node?.orderNumber || 0)
      );
      setOrderedList(sortedList);
    }
  }, [loading]);

  // Calculate the number of groups
  const itemsPerGroup = 6;
  const numGroups = Math.ceil(orderedList?.length / itemsPerGroup);

  // Create an array of groups, each containing up to 6 items
  const groups = Array.from({ length: numGroups }, (_, index) =>
    orderedList?.slice(index * itemsPerGroup, (index + 1) * itemsPerGroup)
  );

  /**
   * Swiper navigation HTML div Element reference
   */
  const prevRef = useRef<HTMLDivElement>(null);
  const nextRef = useRef<HTMLDivElement>(null);
  return (
    <>
      <style jsx>{`
        .reviews-subscription__background {
          background: url("${backgroundImage}");
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      `}</style>

      {loading && <ReviewSliderSkelton />}
      {testimonialReview && (
        <div
          className={`reviews-subscription__background reviews-slider ${styles["reviews-slider"]}`}
        >
          <Swiper
            className={`container`}
            // install Swiper modules
            modules={[Navigation]}
            autoplay={false}
            spaceBetween={0}
            slidesPerView={1}
            pagination={false}
            dir={direction}
            navigation={{
              prevEl: prevRef.current ? prevRef.current : undefined,
              nextEl: nextRef.current ? nextRef.current : undefined,
            }}
            onInit={(swiper) => {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              // eslint-disable-next-line no-param-reassign
              swiper.params.navigation.prevEl = prevRef.current;
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              // eslint-disable-next-line no-param-reassign
              swiper.params.navigation.nextEl = nextRef.current;
              swiper.navigation.update();
            }}
          >
            {groups.map((item: any) => (
              <SwiperSlide key={item?.node?.id} data-testid="testimonialsSlide">
                <SliderItem item={item} />
              </SwiperSlide>
            ))}
            <div
              className="swiper-button-next"
              ref={nextRef}
              data-testid="nextNav"
            >
              <Image src={navIcon} alt="Image" width={48} height={48} />
            </div>
            <div
              className="swiper-button-prev"
              ref={prevRef}
              data-testid="prevNav"
            >
              <Image src={navIcon} alt="Image" width={48} height={48} />
            </div>
          </Swiper>
        </div>
      )}
    </>
  );
};

export default SliderCard;
