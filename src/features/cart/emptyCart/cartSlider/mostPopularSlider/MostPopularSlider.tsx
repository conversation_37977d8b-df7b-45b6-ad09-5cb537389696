import React, { useRef, useState } from "react";
import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { useTranslation } from "next-i18next";
import { Navigation } from "swiper/modules";
import type { Swiper as SwiperType } from 'swiper';
import { SliderInterface } from "@interfaces/common.inteface";
import styles from "./CardsSlider.module.scss";
import getDirection from "@utils/getDirection";
import { PAGEURLS, SLIDER_TYPE } from "@constants/common";
import { BRAND_FILTER_TYPE } from "@constants/allBrandsConfig";
import GiftCard from "@features/common/giftCard/GiftCard";
import useAppRouter from "@features/common/router.context";



interface CardSlider {
  cardsList: SliderInterface;
  slidesPerView?: number;
  spaceBetween?: number;
  mdSlidesPerView?: number;
  mdSpaceBetween?: number;
  lgSlidesPerView?: number;
  lgSpaceBetween?: number;
}
/**
 * @method CardsSlider
 * @description Swiper slide component
 * @param CardSlider : Swiper slide params
 * @returns {JSX.Element}
 */

const MostPopularSlider = ({
  cardsList,
  slidesPerView = 3,
  spaceBetween = 20,
  mdSlidesPerView = 4,
  mdSpaceBetween = 25,
  lgSlidesPerView = 5,
  lgSpaceBetween = 30,
}: CardSlider): JSX.Element => {
  // translations
  const { t } = useTranslation("common");
  const [nextDisabled, setNextDisabled] = useState<string>("");
  const [prevDisabled, setPrevDisabled] = useState<string>("disabled");
  const [rightShadowVisible, setRightShadowVisible] = useState<boolean>(true);
  const [leftShadowVisible, setLeftShadowVisible] = useState<boolean>(false);

  // #. Get the router context
  const { state: routerState } = useAppRouter();
  const locale = routerState?.locale;

  // setup for current direction
  const direction = getDirection(locale);
  // cardlist data
  const ctaText = cardsList?.ctaText;
  const heading = cardsList?.heading;
  const sliderType = cardsList?.sliderType;
  const sliderBrand = cardsList?.sliderBrand;
  const sliderCategories = cardsList?.sliderCategories;
  const seoName = cardsList?.category?.seoName;

  /**
   * Removing 'disabled' class from prev nav when transition slide to next
   */
  const slideNextTransitionStart = () => {
    setPrevDisabled("");
  };

  /**
   * Removing 'disabled' class from next nav when transition slide to prev
   */
  const slidePrevTransitionStart = () => {
    setNextDisabled("");
  };

  /**
   * Adding 'disabled' class on slide reach beginning
   */
  const reachBeginning = () => {
    setPrevDisabled("disabled");
    setLeftShadowVisible(false);
    setRightShadowVisible(true);
  };

  /**
   * Adding 'disabled' class on slide reach End
   */
  const reachEnd = () => {
    setNextDisabled("disabled");
    setRightShadowVisible(false);
    setLeftShadowVisible(true);
  };

  /**
   * Swiper navigation HTML div Element reference
   */
  const prevRef = useRef<HTMLDivElement>(null);
  const nextRef = useRef<HTMLDivElement>(null);

  // autoplay configuration
  const autoplayConfig =
    sliderType === SLIDER_TYPE.CATEGORY
      ? { disableOnInteraction: false }
      : false;

  return (
    <div
      className={`cards-slider ${styles["cards-slider"]}`}
      id="e2eTestingCardSliderWrapper"
      data-testid="cardSliderWrapper"
    >
      <div className={`container ${styles["cards-slider__container"]}`}>
        <div className={styles["cards-slider__header"]}>
          <h2
            className={styles["right-border"]}
            data-testid="cardSliderHeading"
          >
            {t("emptycartheading")}
          </h2>
          <Link legacyBehavior
            href={`${PAGEURLS.ALL_BRANDS}/${BRAND_FILTER_TYPE.CATEGORY}/${cardsList.category?.seoName}`}
          >
            <a
              className={styles["see-more-link"]}
              data-testid="cardSlidermoreLink"
            >
              {ctaText}
            </a>
          </Link>
        </div>
        {(sliderBrand || sliderCategories) && (
          <div
            className={styles["cards-slider__swiper"]}
            data-testid="swiperSliderWrapper"
          >
            <Swiper
              modules={[Navigation]}
              speed={1000}
              slidesPerView={
                sliderType === SLIDER_TYPE.BRAND ? slidesPerView : 5
              }
              spaceBetween={spaceBetween}
              slidesPerGroup={Math.round(slidesPerView - 1)}
              navigation={{
                prevEl: prevRef.current ? prevRef.current : undefined,
                nextEl: nextRef.current ? nextRef.current : undefined,
              }}
              onInit={(swiper) => {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                // eslint-disable-next-line no-param-reassign
                swiper.params.navigation.prevEl = prevRef.current;
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                // eslint-disable-next-line no-param-reassign
                swiper.params.navigation.nextEl = nextRef.current;
                swiper.navigation.update();
              }}
              breakpoints={{
                1065: {
                  slidesPerView:
                    sliderType === SLIDER_TYPE.BRAND ? mdSlidesPerView : 6,
                  spaceBetween: mdSpaceBetween,
                  slidesPerGroup: Math.round(mdSlidesPerView - 1),
                },
                1321: {
                  slidesPerView:
                    sliderType === SLIDER_TYPE.BRAND ? lgSlidesPerView : 7,
                  spaceBetween: lgSpaceBetween,
                  slidesPerGroup: Math.round(lgSlidesPerView - 1),
                },
              }}
              // install Swiper modules
              dir={direction}
              className={`rounded ${styles["cards-slider__swiper-container"]} ${
                sliderType === SLIDER_TYPE.BRAND
                  ? styles["gift-swiper-container"]
                  : styles["category-swiper-container"]
              }`}
              onSlideChange={(swiper) => {
                // category slider stop autoslide when its reach active index 15

                if (
                  swiper?.activeIndex >= 15 &&
                  sliderType === SLIDER_TYPE.CATEGORY
                ) {
                  swiper.autoplay.stop();
                } else {
                  swiper.autoplay.start();
                }

                if (swiper.activeIndex) {
                  setLeftShadowVisible(true);
                  setRightShadowVisible(true);
                }
              }}
              onSlideNextTransitionStart={slideNextTransitionStart}
              onSlidePrevTransitionStart={slidePrevTransitionStart}
              onReachBeginning={reachBeginning}
              onReachEnd={reachEnd}
              autoplay={autoplayConfig}
            >
              <div
                className={`${styles["swiper-nav-wrapper"]} ${
                  styles[sliderType === SLIDER_TYPE.BRAND ? "gift" : "category"]
                }`}
              >
                <div
                  ref={prevRef}
                  className={`${styles["prev"]} prev-nav ${styles[prevDisabled]}`}
                  data-testid="prevNav"
                >
                  <i className="icon-right-arrow"></i>
                </div>
                <div
                  ref={nextRef}
                  className={`${styles["next"]} next-nav ${styles[nextDisabled]}`}
                  data-testid="nextNav"
                >
                  <i className="icon-right-arrow"></i>
                </div>
              </div>
              {sliderType === SLIDER_TYPE.BRAND &&
                sliderBrand?.edges.map((props, index: number) => (
                  <SwiperSlide key={index} data-testid="cardSliderBrandSlide">
                    <GiftCard
                      data={props?.node?.brandData?.brand}
                      seoName={seoName}
                    />
                  </SwiperSlide>
                ))}
              <SwiperSlide>
                <div></div>
              </SwiperSlide>

              {rightShadowVisible && (
                <>
                  <div
                    className={`${styles["slider-right-shadow-after"]} ${
                      styles[
                        "shadow-right-after-" +
                          (sliderType === SLIDER_TYPE.CATEGORY
                            ? "category"
                            : "gift")
                      ]
                    }`}
                  ></div>
                  <div
                    className={`${styles["slider-right-shadow-before"]} ${
                      styles[
                        "shadow-right-before-" +
                          (sliderType === SLIDER_TYPE.CATEGORY
                            ? "category"
                            : "gift")
                      ]
                    }`}
                  ></div>
                </>
              )}
            </Swiper>
          </div>
        )}
      </div>
    </div>
  );
};

export default MostPopularSlider;
