import React, { useEffect, useState, useRef } from "react";
import styles from "./BrandGreetingsSwiper.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import GridSlider from "../brandGridSlider/BrandGridSlider";
import getDirection from "@utils/getDirection";
import getConfig from "next/config";
import BrandGridSkelton from "@features/brand/contentLoader/brandGridSkelton/BrandGridSkelton";
import useAppRouter from "@features/common/router.context";
import useBrand, { BrandContextAction } from "@features/brand/brand.context";
import {
  BRAND_FORM_STATE,
  BRAND_STEPPER,
  BRAND_STEPPER_STATE,
} from "@features/brand/constants/brand.constant";

const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const nextImage = `${imageBaseUrl}/icons/arrow-circle-fw.svg`;

const BrandGreetingsSwiper = ({
  illustrationData,
  gif,
  loading,
  slidePerView,
  onGreetingSelected,
  hasPersonalisedContent,
}: any): JSX.Element => {


  // getting locale
  const {
    state: { locale },
  } = useAppRouter();

  const {
    state: { card },
    dispatch,
  } = useBrand();

  // setup for current direction
  const direction = getDirection(locale);

  //states for slider length
  const [sliderTabs, setSliderTabs] = useState(0);

  //state for card count
  const [cardCount, setCardCount] = useState(0);

  const [data, setData] = useState();
  const [active, setActive] = useState();

  //functions to calculate slider length
  const sliderLength = () => {
    if (
      illustrationData?.gifIllustrations?.edges?.length ||
      illustrationData?.illustrations?.edges?.length
    ) {
      if (gif === true) {
        let sliderlength = Math.ceil(
          illustrationData?.gifIllustrations?.edges?.length / slidePerView
        );
        setSliderTabs(sliderlength);
        setCardCount(illustrationData?.gifIllustrations?.edges?.length);
        setData(illustrationData?.gifIllustrations?.edges);
      } else {
        let sliderlength = Math.ceil(
          illustrationData?.illustrations?.edges?.length / slidePerView
        );
        setSliderTabs(sliderlength);
        setCardCount(illustrationData?.illustrations?.edges?.length);
        setData(illustrationData?.illustrations?.edges);
      }
    }
  };

  /**
   * @method getActiveImageOnLoad
   */
  const getActiveImageOnLoad = () => {
    const image: any = (data || []).find((item: any) => {
      const imageFile = gif ? item?.node?.gifFile : item?.node?.cardImage;
      return card?.greetingCover?.filePath === imageFile;
    });

    if (image) {
      onActive(image, gif);
    }
  };

  /**
   * @method opUpdateSwiper
   */
  const opUpdateSwiper = () => {
    getActiveImageOnLoad();
  };

  /**
   * @method onActive
   * @description Set selected image
   * @param item
   * @param isGif
   */
  const onActive = (item: any, isGif: boolean) => {
    const activeItem = isGif ? item?.node?.gifFile : item?.node?.cardImage;
    setActive(activeItem);

    // #. Dispatch the selcted details
    const filePath = isGif ? item?.node?.gifFile : item?.node?.cardImage || "";
    const referenceCode = item?.node?.referenceCode || "";
    const staticGifPath = item?.node?.gifImage || "";
    onGreetingSelected &&
      onGreetingSelected({ filePath, referenceCode, staticGifPath });
  };

  useEffect(() => {
    !loading && sliderLength();
  }, [loading, illustrationData]);

  useEffect(() => {
    if (card?.formState === BRAND_FORM_STATE.EDIT) {
      dispatch({
        type: BrandContextAction.STEPPER,
        payload: {
          activeStep: BRAND_STEPPER.GREETING,
        },
      });
    }
  }, []);

  /**
   * Swiper navigation HTML div Element reference
   */
  const prevRef = useRef<HTMLDivElement>(null);
  const nextRef = useRef<HTMLDivElement>(null);

  return (
    <>
      {loading ? (
        <BrandGridSkelton />
      ) : (
        <div
          className={`greetings ${styles["grid-container"]}`}
          data-testid="swiperWrapper"
        >
          <Swiper
            modules={[Navigation]}
            dir={direction}
            spaceBetween={0}
            slidesPerView={1}
            allowTouchMove={true}
            navigation={{
              prevEl: prevRef.current ? prevRef.current : undefined,
              nextEl: nextRef.current ? nextRef.current : undefined,
            }}
            onUpdate={() => {
              opUpdateSwiper();
            }}
            onInit={(swiper) => {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              // eslint-disable-next-line no-param-reassign
              swiper.params.navigation.prevEl = prevRef.current;
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              // eslint-disable-next-line no-param-reassign
              swiper.params.navigation.nextEl = nextRef.current;
              swiper.navigation.update();
            }}
            pagination={false}
            autoplay={false}
          >
            {!loading &&
              [...Array(sliderTabs)].map((_, index) => (
                <SwiperSlide key={index}>
                  <GridSlider
                    illustrationData={illustrationData}
                    slidePerView={slidePerView}
                    index={index}
                    gif={gif}
                    data={data}
                    onActive={onActive}
                    selected={active}
                    hasPersonalisedContent={hasPersonalisedContent}
                    loading={loading}
                  />
                </SwiperSlide>
              ))}

            <div
              className={`${cardCount < 10 && styles["hide"]} button-container`}
            >
              <div
                className="swiper-button-next"
                ref={nextRef}
                data-testid="nextNav"
              >
                <img src={nextImage} alt="Image" />
              </div>
              <div
                className={`swiper-button-prev`}
                ref={prevRef}
                data-testid="prevNav"
              >
                <img src={nextImage} alt="Image" />
              </div>
            </div>
          </Swiper>
          {/* {cardCount >= 20 && (
            <>
              <h6
                className={styles["grid-container__text"]}
                data-testid="swiperText"
              >
                {cardCount - 2}+ Greeting Card(s)
              </h6>
            </>
          )} */}
        </div>
      )}
    </>
  );
};

export default BrandGreetingsSwiper;
