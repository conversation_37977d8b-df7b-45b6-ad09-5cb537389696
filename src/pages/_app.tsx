import "../styles/globals.scss";
import type { AppProps } from "next/app";
import { appWithTranslation } from "next-i18next";
import { Router, useRouter } from "next/router";
import { useEffect, useState } from "react";
import getDirection from "@utils/getDirection";
import { ApolloProvider } from "@apollo/client";
import { useApollo } from "@graphql/apolloClient";
import getConfig from "next/config";
import Loader from "@features/common/loader/Loader";
import { useAppDispatch, useAppSelector } from "@redux/hooks";
import {
  getEnableLoader,
  selectIsLoading,
  setAtWorkEnabled,
  setTokenInfo,
} from "@features/common/commonSlice";
import { wrapper } from "@redux/store";
import "simplebar-react/dist/simplebar.min.css";
import 'swiper/scss';
import 'swiper/scss/navigation';
import 'swiper/scss/grid';
import 'swiper/scss/pagination';
import 'swiper/scss/effect-fade';
import { AppRouterContextProvider } from "@features/common/router.context";
import { Amplify } from "aws-amplify";
import { COGNITO_CONFIG, COGNITO_OAUTH_SCOPES } from "@constants/common";
import { ipAddress } from "@features/signinSignup/signInSignUpSlice";
import useCart from "@features/cart/cartSlice";
import { ErrorBoundary } from "@features/common/errorBoundary/ErrorBoundary";
import BackSoon from "./back-soon";
import Clevertap from "@features/common/clevertap/Clevertap";
import CheckCookie from "@features/common/checkCookie/CheckCookie";
import cookieIsEnabled from "@utils/cookieEnabled";

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter();

  const [locale, region] = ((router.locale as any) || "")?.split("-");

  const clevertapAccountId =
    pageProps?.commonData?.data?.siteConfigs?.edges[0]?.node
      ?.clevertapAccountId;
  const apolloClient = useApollo(pageProps, locale);

  // #. Get the default loading state
  const isLoading = useAppSelector(selectIsLoading);
  const enableLoader = useAppSelector(getEnableLoader);

  const dispatch = useAppDispatch();
  const { setBasicCartData } = useCart();

  // #. State to manage the loader component
  const [loading, setLoading] = useState<boolean>(isLoading);
  const [cookieEnabled, setCookieEnabled] = useState<any>("pending");

  const {
    publicRuntimeConfig: {
      identityPoolId,
      region: regionfromConfig,
      userPoolId,
      userPoolWebClientId,
      domain,
      s3BucketName,
      redirectURL,
      cookieDomain,
    },
  } = getConfig();

  // #. changes for making redirectURL dynamic
  const redirectSignOutURL = redirectURL;

  useEffect(() => {
    /* Configuration for AWS Cognito */

    if (cookieEnabled == true && locale) {
      let redirectSignInURL = `${redirectURL}/ae/redirect-signin/`;
      const awsconfig = {
        Auth: {
          identityPoolId: identityPoolId,
          region: regionfromConfig,
          userPoolId: userPoolId,
          userPoolWebClientId: userPoolWebClientId,
          authenticationFlowType: COGNITO_CONFIG.USER_PASSWORD_AUTH_TYPE,
          // cookieStorage: {
          // 			domain: cookieDomain,
          // 			path: '/',
          // 			sameSite: "strict",
          // 			secure: true
          // 		},
        },
        oauth: {
          domain: domain,
          scope: COGNITO_OAUTH_SCOPES,
          redirectSignIn: `${redirectSignInURL}`,
          redirectSignOut: redirectSignOutURL,
          responseType: COGNITO_CONFIG.RESPONSE_TYPE,
        },
        Storage: {
          AWSS3: {
            bucket: s3BucketName,
            region: regionfromConfig,
          },
        },
        ssr: true,
      };
      Amplify.configure(awsconfig);
    }
  }, [locale, cookieEnabled]);

  useEffect(() => {
    document.body.setAttribute("dir", getDirection(locale));
    document.querySelector("html")?.setAttribute("lang", locale);
  }, [locale]);

  // Lazyload fix in Safari
  useEffect(() => {
    const scroll = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 1000);

    return () => {
      clearTimeout(scroll);
    };
  }, [locale, region]);

  // taking public image config url
  const {
    publicRuntimeConfig: { imageBaseUrl },
  } = getConfig();

  useEffect(() => {
    if (cookieIsEnabled()) {
      setCookieEnabled(true);
    } else {
      setCookieEnabled(false);
    }
  }, [locale]);

  useEffect(() => {
    const start = () => {
      setLoading(true);
    };
    const end = () => {
      // setLoading(false);
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    };
    Router.events.on("routeChangeStart", start);
    Router.events.on("routeChangeComplete", end);
    Router.events.on("routeChangeError", end);

    return () => {
      Router.events.off("routeChangeStart", start);
      Router.events.off("routeChangeComplete", end);
      Router.events.off("routeChangeError", end);
    };
  }, []);

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  // Hide loader initailly
  useEffect(() => {
    const load = setTimeout(() => {
      setLoading(false);
    }, 500);
    return () => {
      clearTimeout(load);
    };
  }, []);

  // #. Dispatch the user tokens
  useEffect(() => {
    const accessToken = pageProps?.accessToken;
    const userTokens = {
      AccessToken: accessToken,
      IdToken: pageProps?.idToken,
      RefreshToken: pageProps?.refreshToken,
      isUserSignedIn: pageProps?.isUserSignedIn,
      userAttributes: pageProps?.userAttributes,
      isGuestUser: pageProps?.isGuestUser,
      isGuestEnabled: pageProps?.isGuestEnabled,
    };

    dispatch(setTokenInfo(userTokens));

    if (pageProps?.ip) {
      dispatch(ipAddress({ ip: pageProps?.ip }));
    }
  }, [
    pageProps?.refreshToken,
    pageProps?.accessToken,
    pageProps?.userAttributes,
    pageProps?.isUserSignedIn,
    pageProps?.isGuestUser,
    pageProps?.isGuestEnabled,
  ]);

  const basicCartData = pageProps?.basicCartData;
  useEffect(() => {
    if (basicCartData?.id) {
      dispatch(setBasicCartData(basicCartData));
    }
  }, [basicCartData?.id, basicCartData?.totalQuantity]);

  useEffect(() => {
    // #. Listen the history.replaceState back button event
    const onPopState = function (e: any) {
      const pathName = e?.target?.location?.pathname;
      if (pathName?.indexOf("search/all") !== -1) {
        router?.push(`${pathName?.substring(5)}${e?.target?.location?.search}`);
      }
    };

    window.addEventListener("popstate", onPopState);

    return () => {
      window.removeEventListener("popstate", onPopState);
    };
  }, []);

  // #. flag to show/hide "at work" tab in header.
  const atWorkEnabled =
    pageProps?.commonData?.data?.siteConfigs?.edges[0]?.node
      ?.atworkEnabled;
      
  useEffect(() => {
    dispatch(setAtWorkEnabled(atWorkEnabled));
  }, [atWorkEnabled]);

  return (
    <ApolloProvider client={apolloClient}>
      {clevertapAccountId && (
        <Clevertap clevertapAccountId={clevertapAccountId} />
      )}
      <CheckCookie />
      <AppRouterContextProvider>
        {enableLoader && loading && <Loader />}
        <ErrorBoundary fallback={cookieEnabled ==  true && <BackSoon />}>
          <Component {...pageProps} />
        </ErrorBoundary>
      </AppRouterContextProvider>
      <style jsx global>{`
        @font-face {
          font-family: icomoon;
          src: url("${imageBaseUrl}/fonts/icomoon.eot?pfvxia");
          src: url("${imageBaseUrl}/fonts/icomoon.eot?pfvxia#iefix")
              format("embedded-opentype"),
            url("${imageBaseUrl}/fonts/icomoon.ttf?pfvxia") format("truetype"),
            url("${imageBaseUrl}/fonts/icomoon.woff?pfvxia") format("woff"),
            url("${imageBaseUrl}/fonts/icomoon.svg?pfvxia#icomoon")
              format("svg");
          font-weight: normal;
          font-style: normal;
          font-display: block;
        }
        @font-face {
          font-family: "Lobster1.4";
          src: url("${imageBaseUrl}/fonts/Lobster_1_4.eot?") format("eot"),
            url("Lobster_1_4.woff") format("woff"),
            url("${imageBaseUrl}/fonts/Lobster_1_4.ttf") format("truetype"),
            url("${imageBaseUrl}/fonts/Lobster_1_4.svg#Lobster1.4")
              format("svg");
          font-weight: normal;
          font-style: normal;
        }
      `}</style>
    </ApolloProvider>
  );
}

export default appWithTranslation(wrapper.withRedux(MyApp));
